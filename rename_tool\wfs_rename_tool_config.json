{"dataDir": "../wfsdata", "workerCount": 16, "batchSize": 500, "logLevel": "INFO", "channelBuffer": 32, "progressStep": 10000, "description": {"dataDir": "WFS数据库目录路径，支持相对路径和绝对路径", "workerCount": "并发工作线程数，建议设置为CPU核心数的1-4倍，默认为CPU核心数", "batchSize": "批处理大小，每次从数据库读取的记录数，大数据量建议500-2000", "logLevel": "日志级别，可选值：DEBUG, INFO, WARN, ERROR", "channelBuffer": "任务通道缓冲区大小，控制内存使用，建议为workerCount的2-4倍", "progressStep": "进度报告步长，每处理多少条记录报告一次进度"}, "presets": {"small": {"description": "小数据量配置（< 100万条记录）", "workerCount": 8, "batchSize": 100, "channelBuffer": 16, "progressStep": 1000}, "medium": {"description": "中等数据量配置（100万-1000万条记录）", "workerCount": 12, "batchSize": 300, "channelBuffer": 24, "progressStep": 5000}, "large": {"description": "大数据量配置（> 1000万条记录）", "workerCount": 16, "batchSize": 500, "channelBuffer": 32, "progressStep": 10000}, "ultra": {"description": "超大数据量配置（> 1亿条记录）", "workerCount": 24, "batchSize": 1000, "channelBuffer": 48, "progressStep": 50000}}, "advanced": {"enableDebugMode": false, "enableDryRun": false, "maxRetries": 3, "retryDelay": "1s", "enableBackup": true, "backupDir": "./backup", "enableStatistics": true, "statisticsFile": "./statistics.json"}, "notes": ["配置文件采用JSON格式，支持注释（通过description字段）", "所有路径支持相对路径和绝对路径", "建议根据数据量选择合适的预设配置", "可以通过命令行参数覆盖配置文件中的设置", "修改配置后无需重新编译程序"]}