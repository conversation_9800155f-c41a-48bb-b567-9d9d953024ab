// WFS高性能文件入库工具
// 遍历指定目录的所有子目录和文件，将文件内容插入到WFS数据库
package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/donnie4w/simplelog/logging"
	_ "github.com/donnie4w/wfs/keystore"
	_ "github.com/donnie4w/wfs/stor"
	"github.com/donnie4w/wfs/sys"
)

// 进度统计结构
type ProgressStats struct {
	TotalFiles     int64 // 总文件数
	ProcessedFiles int64 // 已处理文件数
	SuccessCount   int64 // 成功插入数
	ErrorCount     int64 // 错误数
	TotalSize      int64 // 总文件大小
	ProcessedSize  int64 // 已处理大小
	StartTime      time.Time
}

// 文件任务结构
type FileTask struct {
	FilePath string
	FileStem string
	FileSize int64
}

// 入库工具配置
type InsertToolConfig struct {
	SourceDir      string   // 源目录路径
	DataDir        string   // WFS数据目录
	WorkerCount    int      // 并发工作线程数
	ChannelBuffer  int      // 任务通道缓冲区大小
	LogLevel       string   // 日志级别
	ProgressStep   int64    // 进度报告步长
	MaxFileSize    int64    // 最大文件大小限制 (字节)
	SkipExtensions []string // 跳过的文件扩展名
	CompressType   int32    // 压缩类型
}

// 全局变量
var (
	config *InsertToolConfig
	stats  *ProgressStats
	wg     sync.WaitGroup
	ctx    context.Context
	cancel context.CancelFunc
)

func init() {
	// 初始化默认配置 - 针对大数据量优化
	config = &InsertToolConfig{
		SourceDir:      "./source",
		DataDir:        "../wfsdata",
		WorkerCount:    runtime.NumCPU() * 2, // 增加并发数
		ChannelBuffer:  runtime.NumCPU() * 4, // 增加缓冲区
		LogLevel:       "WARN",               // 减少日志输出
		ProgressStep:   50000,                // 大幅增加进度报告间隔
		MaxFileSize:    100 * 1024 * 1024,    // 100MB
		SkipExtensions: []string{".tmp", ".log", ".lock", ".bak", ".swp"},
		CompressType:   0, // 默认压缩类型
	}

	// 初始化统计
	stats = &ProgressStats{
		StartTime: time.Now(),
	}
}

// 从JSON文件加载配置
func loadConfigFromFile(filename string) (*InsertToolConfig, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}

	var config InsertToolConfig
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	return &config, nil
}

// 解析命令行参数
func parseCommandLine() (string, string) {
	var configFile string
	var sourceDir string

	flag.StringVar(&configFile, "config", "", "配置文件路径 (JSON格式)")
	flag.StringVar(&configFile, "c", "", "配置文件路径 (JSON格式) - 简写")
	flag.StringVar(&sourceDir, "source", "", "源目录路径")
	flag.StringVar(&sourceDir, "s", "", "源目录路径 - 简写")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "WFS高性能文件入库工具\n\n")
		fmt.Fprintf(os.Stderr, "用法:\n")
		fmt.Fprintf(os.Stderr, "  %s [选项] [源目录]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "选项:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\n示例:\n")
		fmt.Fprintf(os.Stderr, "  %s ./source                                      # 使用默认配置\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -config config.json                          # 使用配置文件\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -config config.json -source /path/to/files   # 配置文件+源目录\n", os.Args[0])
	}

	flag.Parse()

	// 如果没有通过-source指定，检查位置参数
	if sourceDir == "" && flag.NArg() > 0 {
		sourceDir = flag.Arg(0)
	}

	return configFile, sourceDir
}

// 提取文件stem（去除路径和扩展名）
func extractFileStem(fullPath string) string {
	// 获取文件名（去除路径）
	filename := filepath.Base(fullPath)

	// 去除扩展名
	ext := filepath.Ext(filename)
	if ext != "" {
		filename = strings.TrimSuffix(filename, ext)
	}

	return filename
}

// 检查文件是否应该跳过
func shouldSkipFile(filePath string) bool {
	ext := strings.ToLower(filepath.Ext(filePath))
	for _, skipExt := range config.SkipExtensions {
		if ext == strings.ToLower(skipExt) {
			return true
		}
	}
	return false
}

// 处理单个文件 - 针对大数据量优化
func processFile(task *FileTask) error {
	// 读取文件内容
	data, err := os.ReadFile(task.FilePath)
	if err != nil {
		atomic.AddInt64(&stats.ErrorCount, 1)
		return err
	}

	// 检查文件大小
	if int64(len(data)) > config.MaxFileSize {
		atomic.AddInt64(&stats.ErrorCount, 1)
		return fmt.Errorf("文件过大: %d", len(data))
	}

	// 插入到WFS数据库 - 如果键已存在则删除后重新插入
	if _, err := sys.AppendData(task.FileStem, data, config.CompressType); err != nil {
		// 检查是否是重复键错误（ERR_EXSIT的错误码是4104）
		if err.Equal(sys.ERR_EXSIT) {
			// 删除旧记录后重新插入
			if delErr := sys.DelData(task.FileStem); delErr != nil {
				atomic.AddInt64(&stats.ErrorCount, 1)
				return fmt.Errorf("删除旧记录失败: %v", delErr.Error())
			}
			// 重新插入
			if _, retryErr := sys.AppendData(task.FileStem, data, config.CompressType); retryErr != nil {
				atomic.AddInt64(&stats.ErrorCount, 1)
				return fmt.Errorf("重新插入失败: %v", retryErr.Error())
			}
		} else {
			atomic.AddInt64(&stats.ErrorCount, 1)
			return fmt.Errorf("插入失败: %v", err.Error())
		}
	}

	atomic.AddInt64(&stats.SuccessCount, 1)
	atomic.AddInt64(&stats.ProcessedSize, task.FileSize)

	return nil
}

// 工作线程 - 针对大数据量优化
func worker(taskChan <-chan *FileTask) {
	defer wg.Done()

	for {
		select {
		case task, ok := <-taskChan:
			if !ok {
				return
			}

			if err := processFile(task); err != nil {
				// 只在错误率较高时记录详细错误
				errorCount := atomic.LoadInt64(&stats.ErrorCount)
				if errorCount%1000 == 0 { // 每1000个错误记录一次
					logging.Error(fmt.Sprintf("处理错误 [错误数:%d]: %v", errorCount, err))
				}
			}

			atomic.AddInt64(&stats.ProcessedFiles, 1)

		case <-ctx.Done():
			return
		}
	}
}

// 显示进度 - 针对大数据量优化
func showProgress() {
	ticker := time.NewTicker(5 * time.Second) // 减少更新频率到5秒
	defer ticker.Stop()

	lastProcessed := int64(0)

	for {
		select {
		case <-ticker.C:
			elapsed := time.Since(stats.StartTime)
			processed := atomic.LoadInt64(&stats.ProcessedFiles)
			total := atomic.LoadInt64(&stats.TotalFiles)
			success := atomic.LoadInt64(&stats.SuccessCount)
			errors := atomic.LoadInt64(&stats.ErrorCount)

			// 只有处理数量有变化时才更新显示
			if processed == lastProcessed && total > 0 {
				continue
			}
			lastProcessed = processed

			var progress float64
			if total > 0 {
				progress = float64(processed) / float64(total) * 100
			}

			var eta time.Duration
			if processed > 0 && total > processed && elapsed.Seconds() > 0 {
				rate := float64(processed) / elapsed.Seconds()
				remaining := total - processed
				eta = time.Duration(float64(remaining)/rate) * time.Second
			}

			// 简化输出格式，减少字符串操作
			if total > 0 {
				fmt.Printf("\r进度: %.1f%% (%d/%d) | 成功: %d | 错误: %d | 速度: %.0f/s | 剩余: %v",
					progress, processed, total, success, errors,
					float64(processed)/elapsed.Seconds(), eta.Truncate(time.Second))
			} else {
				fmt.Printf("\r扫描中... 已发现: %d 文件 | 成功: %d | 错误: %d",
					processed, success, errors)
			}

		case <-ctx.Done():
			return
		}
	}
}

// 格式化字节数
func formatBytes(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}

// 扫描目录获取所有文件
func scanDirectory(sourceDir string) ([]*FileTask, error) {
	var tasks []*FileTask
	var totalSize int64

	logging.Info(fmt.Sprintf("开始扫描目录: %s", sourceDir))

	err := filepath.WalkDir(sourceDir, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			logging.Warn(fmt.Sprintf("访问路径失败: %s, 错误: %v", path, err))
			return nil // 继续处理其他文件
		}

		// 跳过目录
		if d.IsDir() {
			return nil
		}

		// 检查是否应该跳过此文件
		if shouldSkipFile(path) {
			logging.Debug(fmt.Sprintf("跳过文件: %s", path))
			return nil
		}

		// 获取文件信息
		info, err := d.Info()
		if err != nil {
			logging.Warn(fmt.Sprintf("获取文件信息失败: %s, 错误: %v", path, err))
			return nil
		}

		fileSize := info.Size()

		// 检查文件大小
		if fileSize > config.MaxFileSize {
			logging.Warn(fmt.Sprintf("跳过大文件: %s (大小: %d > %d)", path, fileSize, config.MaxFileSize))
			return nil
		}

		if fileSize == 0 {
			logging.Debug(fmt.Sprintf("跳过空文件: %s", path))
			return nil
		}

		// 提取文件stem
		stem := extractFileStem(path)
		if stem == "" {
			logging.Warn(fmt.Sprintf("无法提取文件stem: %s", path))
			return nil
		}

		task := &FileTask{
			FilePath: path,
			FileStem: stem,
			FileSize: fileSize,
		}

		tasks = append(tasks, task)
		totalSize += fileSize

		// 定期报告扫描进度
		if int64(len(tasks))%config.ProgressStep == 0 {
			logging.Info(fmt.Sprintf("已扫描 %d 个文件，总大小: %s", len(tasks), formatBytes(totalSize)))
		}

		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("扫描目录失败: %v", err)
	}

	atomic.StoreInt64(&stats.TotalFiles, int64(len(tasks)))
	atomic.StoreInt64(&stats.TotalSize, totalSize)

	logging.Info(fmt.Sprintf("扫描完成，共找到 %d 个文件，总大小: %s", len(tasks), formatBytes(totalSize)))
	return tasks, nil
}

// 流式处理文件 - 针对10亿级文件优化
func processFilesStream(sourceDir string, taskChan chan<- *FileTask) error {
	defer close(taskChan)

	var fileCount int64
	var totalSize int64
	var skipCount int64

	fmt.Printf("开始扫描目录: %s\n", sourceDir)

	err := filepath.WalkDir(sourceDir, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			// 只记录严重错误，减少日志输出
			if strings.Contains(err.Error(), "permission denied") {
				atomic.AddInt64(&skipCount, 1)
			}
			return nil
		}

		// 跳过目录
		if d.IsDir() {
			return nil
		}

		// 快速检查文件扩展名（避免字符串操作）
		if shouldSkipFile(path) {
			atomic.AddInt64(&skipCount, 1)
			return nil
		}

		// 获取文件信息（使用DirEntry.Info()更高效）
		info, err := d.Info()
		if err != nil {
			atomic.AddInt64(&skipCount, 1)
			return nil
		}

		fileSize := info.Size()

		// 快速过滤
		if fileSize > config.MaxFileSize || fileSize == 0 {
			atomic.AddInt64(&skipCount, 1)
			return nil
		}

		// 提取文件stem（优化字符串操作）
		stem := extractFileStem(path)
		if stem == "" {
			atomic.AddInt64(&skipCount, 1)
			return nil
		}

		// 复用任务对象减少内存分配
		task := &FileTask{
			FilePath: path,
			FileStem: stem,
			FileSize: fileSize,
		}

		select {
		case taskChan <- task:
			fileCount++
			totalSize += fileSize

			// 大幅减少进度报告频率
			if fileCount%config.ProgressStep == 0 {
				fmt.Printf("扫描进度: %d 文件 (跳过: %d)\n", fileCount, atomic.LoadInt64(&skipCount))
			}

		case <-ctx.Done():
			fmt.Printf("扫描被中断，已发送 %d 个文件\n", fileCount)
			return ctx.Err()
		}

		return nil
	})

	if err != nil {
		return fmt.Errorf("流式处理失败: %v", err)
	}

	atomic.StoreInt64(&stats.TotalFiles, fileCount)
	atomic.StoreInt64(&stats.TotalSize, totalSize)

	fmt.Printf("扫描完成: %d 文件, %s (跳过: %d)\n", fileCount, formatBytes(totalSize), atomic.LoadInt64(&skipCount))
	return nil
}

// 主处理函数 - 使用流式处理
func processAllFiles() error {
	// 创建上下文
	ctx, cancel = context.WithCancel(context.Background())
	defer cancel()

	// 检查源目录是否存在
	if _, err := os.Stat(config.SourceDir); os.IsNotExist(err) {
		return fmt.Errorf("源目录不存在: %s", config.SourceDir)
	}

	fmt.Printf("准备处理目录: %s\n", config.SourceDir)

	// 创建任务通道
	taskChan := make(chan *FileTask, config.ChannelBuffer)

	// 启动工作线程
	for i := 0; i < config.WorkerCount; i++ {
		wg.Add(1)
		go worker(taskChan)
	}

	// 启动进度显示
	go showProgress()

	// 启动流式文件处理
	go func() {
		if err := processFilesStream(config.SourceDir, taskChan); err != nil {
			logging.Error(fmt.Sprintf("流式处理失败: %v", err))
			cancel()
		}
	}()

	// 等待所有任务完成
	wg.Wait()

	// 显示最终统计 - 简化输出
	fmt.Printf("\n\n=== 处理完成 ===\n")
	totalFiles := atomic.LoadInt64(&stats.TotalFiles)
	processedFiles := atomic.LoadInt64(&stats.ProcessedFiles)
	successCount := atomic.LoadInt64(&stats.SuccessCount)
	errorCount := atomic.LoadInt64(&stats.ErrorCount)
	elapsed := time.Since(stats.StartTime)

	fmt.Printf("文件统计: %d 总数 | %d 成功 | %d 错误\n", totalFiles, successCount, errorCount)
	fmt.Printf("处理时间: %v\n", elapsed.Truncate(time.Second))

	// 计算处理速度
	if elapsed.Seconds() > 0 {
		filesPerSec := float64(processedFiles) / elapsed.Seconds()
		fmt.Printf("处理速度: %.0f 文件/秒\n", filesPerSec)
	}

	// 显示错误率
	if totalFiles > 0 {
		errorRate := float64(errorCount) / float64(totalFiles) * 100
		fmt.Printf("成功率: %.2f%%\n", 100-errorRate)
	}

	return nil
}

// 初始化系统 - 只打开已有数据库，不进行初始化
func initSystem() error {
	// 检查数据目录是否存在
	if _, err := os.Stat(config.DataDir); os.IsNotExist(err) {
		return fmt.Errorf("数据目录不存在: %s", config.DataDir)
	}

	// 检查数据库目录是否存在
	dbDir := filepath.Join(config.DataDir, "wfsdb")
	if _, err := os.Stat(dbDir); os.IsNotExist(err) {
		return fmt.Errorf("WFS数据库不存在: %s，请先使用wfs.exe初始化数据库", dbDir)
	}

	// 设置数据目录（但不初始化）
	sys.WFSDATA = config.DataDir

	// 初始化日志
	logDir := filepath.Join(config.DataDir, "logs")
	if err := os.MkdirAll(logDir, 0755); err != nil {
		return fmt.Errorf("创建日志目录失败: %v", err)
	}

	logging.SetRollingFile(logDir, "insert_tool.log", 100, logging.MB)

	// 设置日志级别
	switch strings.ToUpper(config.LogLevel) {
	case "DEBUG":
		logging.SetLevel(logging.LEVEL_DEBUG)
	case "INFO":
		logging.SetLevel(logging.LEVEL_INFO)
	case "WARN":
		logging.SetLevel(logging.LEVEL_WARN)
	case "ERROR":
		logging.SetLevel(logging.LEVEL_ERROR)
	default:
		logging.SetLevel(logging.LEVEL_INFO)
	}

	// 只读模式初始化存储引擎 - 禁止任何数据文件初始化
	if err := initStoreReadOnly(); err != nil {
		return fmt.Errorf("只读模式初始化存储引擎失败: %v", err)
	}

	// 验证关键函数是否可用
	if sys.AppendData == nil {
		return fmt.Errorf("存储引擎未正确初始化：AppendData函数不可用")
	}

	fmt.Printf("已连接到现有WFS数据库: %s\n", dbDir)
	return nil
}

// 只读模式初始化存储引擎 - 禁止创建新的数据文件
func initStoreReadOnly() error {
	// 首先正常初始化存储引擎，允许使用现有的文件处理器
	if server, ok := sys.Serve.Get(1); ok {
		if err := server.Serve(); err != nil {
			return fmt.Errorf("打开存储引擎失败: %v", err)
		}
	} else {
		return fmt.Errorf("存储引擎服务未注册")
	}

	// 初始化完成后，设置只读模式标志，防止后续创建新数据文件
	sys.ReadOnlyMode = true
	fmt.Println("已启用只读模式，禁止创建新数据文件")

	return nil
}

func main() {
	fmt.Println("WFS高性能文件入库工具 v1.0")
	fmt.Println("=============================")

	// 确保程序退出时清理资源
	defer cleanup()

	// 解析命令行参数
	configFile, sourceDir := parseCommandLine()

	// 如果指定了配置文件，加载配置
	if configFile != "" {
		if newConfig, err := loadConfigFromFile(configFile); err != nil {
			fmt.Printf("配置文件加载失败: %v\n", err)
			fmt.Println("使用默认配置...")
		} else {
			config = newConfig
			fmt.Printf("配置文件: %s\n", configFile)
		}
	}

	// 命令行指定的源目录优先级最高
	if sourceDir != "" {
		config.SourceDir = sourceDir
	}

	// 简化配置显示
	fmt.Printf("源目录: %s\n", config.SourceDir)
	fmt.Printf("并发数: %d | 缓冲区: %d | 最大文件: %s\n",
		config.WorkerCount, config.ChannelBuffer, formatBytes(config.MaxFileSize))
	fmt.Println()

	// 初始化系统
	if err := initSystem(); err != nil {
		fmt.Printf("初始化失败: %v\n", err)
		os.Exit(1)
	}

	// 开始处理
	if err := processAllFiles(); err != nil {
		fmt.Printf("处理失败: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("\n程序执行完成！")

	// 清理资源
	cleanup()
}

// 清理资源
func cleanup() {
	// 清理工作（如果需要的话）
}
