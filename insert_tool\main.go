// WFS高性能文件入库工具
// 遍历指定目录的所有子目录和文件，将文件内容插入到WFS数据库
package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/donnie4w/simplelog/logging"
	_ "github.com/donnie4w/wfs/keystore"
	_ "github.com/donnie4w/wfs/stor"
	"github.com/donnie4w/wfs/sys"
)

// 进度统计结构
type ProgressStats struct {
	TotalFiles     int64 // 总文件数
	ProcessedFiles int64 // 已处理文件数
	SuccessCount   int64 // 成功插入数
	ErrorCount     int64 // 错误数
	TotalSize      int64 // 总文件大小
	ProcessedSize  int64 // 已处理大小
	StartTime      time.Time
}

// 文件任务结构
type FileTask struct {
	FilePath string
	FileStem string
	FileSize int64
}

// 入库工具配置
type InsertToolConfig struct {
	SourceDir      string   // 源目录路径
	DataDir        string   // WFS数据目录
	WorkerCount    int      // 并发工作线程数
	ChannelBuffer  int      // 任务通道缓冲区大小
	LogLevel       string   // 日志级别
	ProgressStep   int64    // 进度报告步长
	MaxFileSize    int64    // 最大文件大小限制 (字节)
	SkipExtensions []string // 跳过的文件扩展名
	CompressType   int32    // 压缩类型
}

// 全局变量
var (
	config *InsertToolConfig
	stats  *ProgressStats
	wg     sync.WaitGroup
	ctx    context.Context
	cancel context.CancelFunc
)

func init() {
	// 初始化默认配置
	config = &InsertToolConfig{
		SourceDir:      "./source",
		DataDir:        "../wfsdata",
		WorkerCount:    runtime.NumCPU(),
		ChannelBuffer:  runtime.NumCPU() * 2,
		LogLevel:       "INFO",
		ProgressStep:   100,
		MaxFileSize:    100 * 1024 * 1024, // 100MB
		SkipExtensions: []string{".tmp", ".log", ".lock"},
		CompressType:   0, // 默认压缩类型
	}

	// 初始化统计
	stats = &ProgressStats{
		StartTime: time.Now(),
	}
}

// 从JSON文件加载配置
func loadConfigFromFile(filename string) (*InsertToolConfig, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}

	var config InsertToolConfig
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	return &config, nil
}

// 解析命令行参数
func parseCommandLine() (string, string) {
	var configFile string
	var sourceDir string

	flag.StringVar(&configFile, "config", "", "配置文件路径 (JSON格式)")
	flag.StringVar(&configFile, "c", "", "配置文件路径 (JSON格式) - 简写")
	flag.StringVar(&sourceDir, "source", "", "源目录路径")
	flag.StringVar(&sourceDir, "s", "", "源目录路径 - 简写")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "WFS高性能文件入库工具\n\n")
		fmt.Fprintf(os.Stderr, "用法:\n")
		fmt.Fprintf(os.Stderr, "  %s [选项] [源目录]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "选项:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\n示例:\n")
		fmt.Fprintf(os.Stderr, "  %s ./source                                      # 使用默认配置\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -config config.json                          # 使用配置文件\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -config config.json -source /path/to/files   # 配置文件+源目录\n", os.Args[0])
	}

	flag.Parse()

	// 如果没有通过-source指定，检查位置参数
	if sourceDir == "" && flag.NArg() > 0 {
		sourceDir = flag.Arg(0)
	}

	return configFile, sourceDir
}

// 提取文件stem（去除路径和扩展名）
func extractFileStem(fullPath string) string {
	// 获取文件名（去除路径）
	filename := filepath.Base(fullPath)

	// 去除扩展名
	ext := filepath.Ext(filename)
	if ext != "" {
		filename = strings.TrimSuffix(filename, ext)
	}

	return filename
}

// 检查文件是否应该跳过
func shouldSkipFile(filePath string) bool {
	ext := strings.ToLower(filepath.Ext(filePath))
	for _, skipExt := range config.SkipExtensions {
		if ext == strings.ToLower(skipExt) {
			return true
		}
	}
	return false
}

// 处理单个文件
func processFile(task *FileTask) error {
	// 读取文件内容
	data, err := os.ReadFile(task.FilePath)
	if err != nil {
		atomic.AddInt64(&stats.ErrorCount, 1)
		return fmt.Errorf("读取文件失败: %v", err)
	}

	// 检查文件大小
	if int64(len(data)) > config.MaxFileSize {
		atomic.AddInt64(&stats.ErrorCount, 1)
		return fmt.Errorf("文件大小超过限制: %d > %d", len(data), config.MaxFileSize)
	}

	// 插入到WFS数据库
	if _, err := sys.AppendData(task.FileStem, data, config.CompressType); err != nil {
		atomic.AddInt64(&stats.ErrorCount, 1)
		return fmt.Errorf("插入数据库失败: %v", err)
	}

	atomic.AddInt64(&stats.SuccessCount, 1)
	atomic.AddInt64(&stats.ProcessedSize, task.FileSize)
	logging.Info(fmt.Sprintf("插入成功: %s -> %s (%d bytes)", task.FilePath, task.FileStem, task.FileSize))

	return nil
}

// 工作线程
func worker(taskChan <-chan *FileTask) {
	defer wg.Done()

	for {
		select {
		case task, ok := <-taskChan:
			if !ok {
				return
			}

			if err := processFile(task); err != nil {
				logging.Error(fmt.Sprintf("处理文件失败 [%s]: %v", task.FilePath, err))
			}

			atomic.AddInt64(&stats.ProcessedFiles, 1)

		case <-ctx.Done():
			return
		}
	}
}

// 显示进度
func showProgress() {
	ticker := time.NewTicker(time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			elapsed := time.Since(stats.StartTime)
			processed := atomic.LoadInt64(&stats.ProcessedFiles)
			total := atomic.LoadInt64(&stats.TotalFiles)
			success := atomic.LoadInt64(&stats.SuccessCount)
			errors := atomic.LoadInt64(&stats.ErrorCount)
			processedSize := atomic.LoadInt64(&stats.ProcessedSize)
			totalSize := atomic.LoadInt64(&stats.TotalSize)

			var progress float64
			if total > 0 {
				progress = float64(processed) / float64(total) * 100
			}

			var sizeProgress float64
			if totalSize > 0 {
				sizeProgress = float64(processedSize) / float64(totalSize) * 100
			}

			var eta time.Duration
			if processed > 0 && total > processed {
				rate := float64(processed) / elapsed.Seconds()
				remaining := total - processed
				eta = time.Duration(float64(remaining)/rate) * time.Second
			}

			// 格式化文件大小
			processedSizeStr := formatBytes(processedSize)
			totalSizeStr := formatBytes(totalSize)

			fmt.Printf("\r进度: %.2f%% (%d/%d) | 大小: %.2f%% (%s/%s) | 成功: %d | 错误: %d | 用时: %v | 预计剩余: %v",
				progress, processed, total, sizeProgress, processedSizeStr, totalSizeStr,
				success, errors, elapsed.Truncate(time.Second), eta.Truncate(time.Second))

		case <-ctx.Done():
			return
		}
	}
}

// 格式化字节数
func formatBytes(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}

// 扫描目录获取所有文件
func scanDirectory(sourceDir string) ([]*FileTask, error) {
	var tasks []*FileTask
	var totalSize int64

	logging.Info(fmt.Sprintf("开始扫描目录: %s", sourceDir))

	err := filepath.WalkDir(sourceDir, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			logging.Warn(fmt.Sprintf("访问路径失败: %s, 错误: %v", path, err))
			return nil // 继续处理其他文件
		}

		// 跳过目录
		if d.IsDir() {
			return nil
		}

		// 检查是否应该跳过此文件
		if shouldSkipFile(path) {
			logging.Debug(fmt.Sprintf("跳过文件: %s", path))
			return nil
		}

		// 获取文件信息
		info, err := d.Info()
		if err != nil {
			logging.Warn(fmt.Sprintf("获取文件信息失败: %s, 错误: %v", path, err))
			return nil
		}

		fileSize := info.Size()

		// 检查文件大小
		if fileSize > config.MaxFileSize {
			logging.Warn(fmt.Sprintf("跳过大文件: %s (大小: %d > %d)", path, fileSize, config.MaxFileSize))
			return nil
		}

		if fileSize == 0 {
			logging.Debug(fmt.Sprintf("跳过空文件: %s", path))
			return nil
		}

		// 提取文件stem
		stem := extractFileStem(path)
		if stem == "" {
			logging.Warn(fmt.Sprintf("无法提取文件stem: %s", path))
			return nil
		}

		task := &FileTask{
			FilePath: path,
			FileStem: stem,
			FileSize: fileSize,
		}

		tasks = append(tasks, task)
		totalSize += fileSize

		// 定期报告扫描进度
		if int64(len(tasks))%config.ProgressStep == 0 {
			logging.Info(fmt.Sprintf("已扫描 %d 个文件，总大小: %s", len(tasks), formatBytes(totalSize)))
		}

		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("扫描目录失败: %v", err)
	}

	atomic.StoreInt64(&stats.TotalFiles, int64(len(tasks)))
	atomic.StoreInt64(&stats.TotalSize, totalSize)

	logging.Info(fmt.Sprintf("扫描完成，共找到 %d 个文件，总大小: %s", len(tasks), formatBytes(totalSize)))
	return tasks, nil
}

// 流式处理文件 - 避免内存占用过大
func processFilesStream(sourceDir string, taskChan chan<- *FileTask) error {
	defer close(taskChan)

	var fileCount int64
	var totalSize int64

	logging.Info(fmt.Sprintf("开始流式处理目录: %s", sourceDir))

	err := filepath.WalkDir(sourceDir, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			logging.Warn(fmt.Sprintf("访问路径失败: %s, 错误: %v", path, err))
			return nil
		}

		// 跳过目录
		if d.IsDir() {
			return nil
		}

		// 检查是否应该跳过此文件
		if shouldSkipFile(path) {
			logging.Debug(fmt.Sprintf("跳过文件: %s", path))
			return nil
		}

		// 获取文件信息
		info, err := d.Info()
		if err != nil {
			logging.Warn(fmt.Sprintf("获取文件信息失败: %s, 错误: %v", path, err))
			return nil
		}

		fileSize := info.Size()

		// 检查文件大小
		if fileSize > config.MaxFileSize {
			logging.Warn(fmt.Sprintf("跳过大文件: %s (大小: %d > %d)", path, fileSize, config.MaxFileSize))
			return nil
		}

		if fileSize == 0 {
			logging.Debug(fmt.Sprintf("跳过空文件: %s", path))
			return nil
		}

		// 提取文件stem
		stem := extractFileStem(path)
		if stem == "" {
			logging.Warn(fmt.Sprintf("无法提取文件stem: %s", path))
			return nil
		}

		task := &FileTask{
			FilePath: path,
			FileStem: stem,
			FileSize: fileSize,
		}

		select {
		case taskChan <- task:
			fileCount++
			totalSize += fileSize

			// 定期报告进度
			if fileCount%config.ProgressStep == 0 {
				logging.Info(fmt.Sprintf("已发送 %d 个文件到处理队列，总大小: %s", fileCount, formatBytes(totalSize)))
			}

		case <-ctx.Done():
			logging.Info(fmt.Sprintf("处理被中断，已发送 %d 个文件", fileCount))
			return ctx.Err()
		}

		return nil
	})

	if err != nil {
		return fmt.Errorf("流式处理失败: %v", err)
	}

	atomic.StoreInt64(&stats.TotalFiles, fileCount)
	atomic.StoreInt64(&stats.TotalSize, totalSize)

	logging.Info(fmt.Sprintf("流式扫描完成，共发送 %d 个文件，总大小: %s", fileCount, formatBytes(totalSize)))
	return nil
}

// 主处理函数 - 使用流式处理
func processAllFiles() error {
	// 创建上下文
	ctx, cancel = context.WithCancel(context.Background())
	defer cancel()

	// 检查源目录是否存在
	if _, err := os.Stat(config.SourceDir); os.IsNotExist(err) {
		return fmt.Errorf("源目录不存在: %s", config.SourceDir)
	}

	fmt.Printf("准备处理目录: %s\n", config.SourceDir)

	// 创建任务通道
	taskChan := make(chan *FileTask, config.ChannelBuffer)

	// 启动工作线程
	for i := 0; i < config.WorkerCount; i++ {
		wg.Add(1)
		go worker(taskChan)
	}

	// 启动进度显示
	go showProgress()

	// 启动流式文件处理
	go func() {
		if err := processFilesStream(config.SourceDir, taskChan); err != nil {
			logging.Error(fmt.Sprintf("流式处理失败: %v", err))
			cancel()
		}
	}()

	// 等待所有任务完成
	wg.Wait()

	// 显示最终统计
	fmt.Printf("\n\n处理完成！\n")
	fmt.Printf("总文件数: %d\n", atomic.LoadInt64(&stats.TotalFiles))
	fmt.Printf("已处理数: %d\n", atomic.LoadInt64(&stats.ProcessedFiles))
	fmt.Printf("成功插入: %d\n", atomic.LoadInt64(&stats.SuccessCount))
	fmt.Printf("错误数量: %d\n", atomic.LoadInt64(&stats.ErrorCount))
	fmt.Printf("总文件大小: %s\n", formatBytes(atomic.LoadInt64(&stats.TotalSize)))
	fmt.Printf("已处理大小: %s\n", formatBytes(atomic.LoadInt64(&stats.ProcessedSize)))
	fmt.Printf("总用时: %v\n", time.Since(stats.StartTime).Truncate(time.Second))

	// 计算处理速度
	elapsed := time.Since(stats.StartTime)
	if elapsed.Seconds() > 0 {
		filesPerSec := float64(atomic.LoadInt64(&stats.ProcessedFiles)) / elapsed.Seconds()
		bytesPerSec := float64(atomic.LoadInt64(&stats.ProcessedSize)) / elapsed.Seconds()
		fmt.Printf("处理速度: %.2f 文件/秒, %s/秒\n", filesPerSec, formatBytes(int64(bytesPerSec)))
	}

	return nil
}

// 初始化系统
func initSystem() error {
	// 设置数据目录
	sys.WFSDATA = config.DataDir

	// 初始化日志
	logDir := filepath.Join(config.DataDir, "logs")
	if err := os.MkdirAll(logDir, 0755); err != nil {
		return fmt.Errorf("创建日志目录失败: %v", err)
	}

	logging.SetRollingFile(logDir, "insert_tool.log", 100, logging.MB)

	// 设置日志级别
	switch strings.ToUpper(config.LogLevel) {
	case "DEBUG":
		logging.SetLevel(logging.LEVEL_DEBUG)
	case "INFO":
		logging.SetLevel(logging.LEVEL_INFO)
	case "WARN":
		logging.SetLevel(logging.LEVEL_WARN)
	case "ERROR":
		logging.SetLevel(logging.LEVEL_ERROR)
	default:
		logging.SetLevel(logging.LEVEL_INFO)
	}

	// 初始化密钥存储
	sys.KeyStoreInit(config.DataDir)

	// 初始化存储引擎 - 通过sys.Serve调用
	if server, ok := sys.Serve.Get(1); ok {
		if err := server.Serve(); err != nil {
			return fmt.Errorf("初始化存储引擎失败: %v", err)
		}
	} else {
		return fmt.Errorf("存储引擎服务未注册")
	}

	return nil
}

func main() {
	fmt.Println("WFS高性能文件入库工具")
	fmt.Println("========================")

	// 解析命令行参数
	configFile, sourceDir := parseCommandLine()

	// 如果指定了配置文件，加载配置
	if configFile != "" {
		if newConfig, err := loadConfigFromFile(configFile); err != nil {
			fmt.Printf("加载配置文件失败: %v\n", err)
			fmt.Println("使用默认配置继续运行...")
		} else {
			config = newConfig
			fmt.Printf("已加载配置文件: %s\n", configFile)
		}
	}

	// 命令行指定的源目录优先级最高
	if sourceDir != "" {
		config.SourceDir = sourceDir
	}

	// 显示当前配置
	fmt.Printf("源目录: %s\n", config.SourceDir)
	fmt.Printf("数据目录: %s\n", config.DataDir)
	fmt.Printf("并发线程数: %d\n", config.WorkerCount)
	fmt.Printf("通道缓冲区: %d\n", config.ChannelBuffer)
	fmt.Printf("进度报告步长: %d\n", config.ProgressStep)
	fmt.Printf("最大文件大小: %s\n", formatBytes(config.MaxFileSize))
	fmt.Printf("跳过扩展名: %v\n", config.SkipExtensions)
	fmt.Printf("压缩类型: %d\n", config.CompressType)
	fmt.Printf("日志级别: %s\n", config.LogLevel)
	fmt.Println()

	// 初始化系统
	if err := initSystem(); err != nil {
		fmt.Printf("初始化失败: %v\n", err)
		os.Exit(1)
	}

	// 开始处理
	if err := processAllFiles(); err != nil {
		fmt.Printf("处理失败: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("程序执行完成！")
}
