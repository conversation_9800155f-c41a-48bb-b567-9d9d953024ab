// WFS文件名批量更名工具
// 基于原WFS系统改造，去除web和thrift服务，专门用于批量文件名更名
package main

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/donnie4w/simplelog/logging"
	_ "github.com/donnie4w/wfs/keystore"
	_ "github.com/donnie4w/wfs/stor"
	"github.com/donnie4w/wfs/sys"
)

// 进度统计结构
type ProgressStats struct {
	TotalCount     int64 // 总记录数
	ProcessedCount int64 // 已处理数
	SuccessCount   int64 // 成功更名数
	DeletedCount   int64 // 删除重复数
	ErrorCount     int64 // 错误数
	StartTime      time.Time
}

// 更名任务结构
type RenameTask struct {
	ID       int64
	OldPath  string
	NewPath  string
	FileStem string
}

// 更名工具主结构
type RenameToolConfig struct {
	DataDir     string // 数据目录
	WorkerCount int    // 并发工作线程数
	BatchSize   int    // 批处理大小
	LogLevel    string // 日志级别
}

// 全局变量
var (
	config *RenameToolConfig
	stats  *ProgressStats
	wg     sync.WaitGroup
	ctx    context.Context
	cancel context.CancelFunc
)

func init() {
	// 初始化配置
	config = &RenameToolConfig{
		DataDir:     "../wfsdata",
		WorkerCount: runtime.NumCPU(),
		BatchSize:   100,
		LogLevel:    "INFO",
	}

	// 初始化统计
	stats = &ProgressStats{
		StartTime: time.Now(),
	}
}

// 提取文件stem（去除路径和扩展名）
func extractFileStem(fullPath string) string {
	// 获取文件名（去除路径）
	filename := filepath.Base(fullPath)

	// 去除扩展名
	ext := filepath.Ext(filename)
	if ext != "" {
		filename = strings.TrimSuffix(filename, ext)
	}

	return filename
}

// 检查文件stem是否已存在
func checkFileStemExists(stem string) bool {
	// 使用sys.SearchLike查找以stem开头的文件
	results := sys.SearchLike(stem)
	for _, result := range results {
		if extractFileStem(result.Path) == stem {
			return true
		}
	}
	return false
}

// 处理单个文件记录
func processFileRecord(task *RenameTask) error {
	// 提取文件stem
	stem := extractFileStem(task.OldPath)
	task.FileStem = stem
	task.NewPath = stem

	// 检查目标stem是否已存在
	if checkFileStemExists(stem) && task.OldPath != stem {
		// 如果存在且不是同一个文件，删除当前记录
		if err := sys.DelData(task.OldPath); err != nil {
			atomic.AddInt64(&stats.ErrorCount, 1)
			return fmt.Errorf("删除重复记录失败: %v", err)
		}
		atomic.AddInt64(&stats.DeletedCount, 1)
		logging.Info(fmt.Sprintf("删除重复记录: %s -> %s", task.OldPath, stem))
	} else if task.OldPath != stem {
		// 执行更名操作
		if err := sys.Modify(task.OldPath, stem); err != nil {
			atomic.AddInt64(&stats.ErrorCount, 1)
			return fmt.Errorf("更名失败: %v", err)
		}
		atomic.AddInt64(&stats.SuccessCount, 1)
		logging.Info(fmt.Sprintf("更名成功: %s -> %s", task.OldPath, stem))
	}

	atomic.AddInt64(&stats.ProcessedCount, 1)
	return nil
}

// 工作线程
func worker(taskChan <-chan *RenameTask) {
	defer wg.Done()

	for {
		select {
		case task, ok := <-taskChan:
			if !ok {
				return
			}

			if err := processFileRecord(task); err != nil {
				logging.Error(fmt.Sprintf("处理任务失败 [ID:%d]: %v", task.ID, err))
			}

		case <-ctx.Done():
			return
		}
	}
}

// 显示进度
func showProgress() {
	ticker := time.NewTicker(time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			elapsed := time.Since(stats.StartTime)
			processed := atomic.LoadInt64(&stats.ProcessedCount)
			total := atomic.LoadInt64(&stats.TotalCount)
			success := atomic.LoadInt64(&stats.SuccessCount)
			deleted := atomic.LoadInt64(&stats.DeletedCount)
			errors := atomic.LoadInt64(&stats.ErrorCount)

			var progress float64
			if total > 0 {
				progress = float64(processed) / float64(total) * 100
			}

			var eta time.Duration
			if processed > 0 && total > processed {
				rate := float64(processed) / elapsed.Seconds()
				remaining := total - processed
				eta = time.Duration(float64(remaining)/rate) * time.Second
			}

			fmt.Printf("\r进度: %.2f%% (%d/%d) | 成功: %d | 删除: %d | 错误: %d | 用时: %v | 预计剩余: %v",
				progress, processed, total, success, deleted, errors,
				elapsed.Truncate(time.Second), eta.Truncate(time.Second))

		case <-ctx.Done():
			return
		}
	}
}

// 获取所有需要处理的记录
func getAllRecords() ([]*RenameTask, error) {
	var tasks []*RenameTask

	// 获取总记录数
	totalCount := sys.Count()
	atomic.StoreInt64(&stats.TotalCount, totalCount)

	logging.Info(fmt.Sprintf("开始扫描数据库，总记录数: %d", totalCount))

	// 分批获取记录
	batchSize := int64(config.BatchSize)
	for start := int64(1); start <= totalCount; start += batchSize {
		limit := batchSize
		if start+limit > totalCount {
			limit = totalCount - start + 1
		}

		records := sys.SearchLimit(start, limit)
		for _, record := range records {
			task := &RenameTask{
				ID:      record.Id,
				OldPath: record.Path,
			}
			tasks = append(tasks, task)
		}
	}

	logging.Info(fmt.Sprintf("扫描完成，获取到 %d 条记录", len(tasks)))
	return tasks, nil
}

// 主处理函数
func processAllFiles() error {
	// 创建上下文
	ctx, cancel = context.WithCancel(context.Background())
	defer cancel()

	// 获取所有记录
	tasks, err := getAllRecords()
	if err != nil {
		return fmt.Errorf("获取记录失败: %v", err)
	}

	if len(tasks) == 0 {
		fmt.Println("没有找到需要处理的记录")
		return nil
	}

	// 创建任务通道
	taskChan := make(chan *RenameTask, config.WorkerCount*2)

	// 启动工作线程
	for i := 0; i < config.WorkerCount; i++ {
		wg.Add(1)
		go worker(taskChan)
	}

	// 启动进度显示
	go showProgress()

	// 发送任务
	go func() {
		defer close(taskChan)
		for _, task := range tasks {
			select {
			case taskChan <- task:
			case <-ctx.Done():
				return
			}
		}
	}()

	// 等待所有任务完成
	wg.Wait()

	// 显示最终统计
	fmt.Printf("\n\n处理完成！\n")
	fmt.Printf("总记录数: %d\n", atomic.LoadInt64(&stats.TotalCount))
	fmt.Printf("已处理数: %d\n", atomic.LoadInt64(&stats.ProcessedCount))
	fmt.Printf("成功更名: %d\n", atomic.LoadInt64(&stats.SuccessCount))
	fmt.Printf("删除重复: %d\n", atomic.LoadInt64(&stats.DeletedCount))
	fmt.Printf("错误数量: %d\n", atomic.LoadInt64(&stats.ErrorCount))
	fmt.Printf("总用时: %v\n", time.Since(stats.StartTime).Truncate(time.Second))

	return nil
}

// 初始化系统
func initSystem() error {
	// 设置数据目录
	sys.WFSDATA = config.DataDir

	// 初始化日志
	logDir := filepath.Join(config.DataDir, "logs")
	if err := os.MkdirAll(logDir, 0755); err != nil {
		return fmt.Errorf("创建日志目录失败: %v", err)
	}

	logging.SetRollingFile(logDir, "rename_tool.log", 100, logging.MB)

	// 设置日志级别
	switch strings.ToUpper(config.LogLevel) {
	case "DEBUG":
		logging.SetLevel(logging.LEVEL_DEBUG)
	case "INFO":
		logging.SetLevel(logging.LEVEL_INFO)
	case "WARN":
		logging.SetLevel(logging.LEVEL_WARN)
	case "ERROR":
		logging.SetLevel(logging.LEVEL_ERROR)
	default:
		logging.SetLevel(logging.LEVEL_INFO)
	}

	// 初始化密钥存储
	sys.KeyStoreInit(config.DataDir)

	// 初始化存储引擎 - 通过sys.Serve调用
	if server, ok := sys.Serve.Get(1); ok {
		if err := server.Serve(); err != nil {
			return fmt.Errorf("初始化存储引擎失败: %v", err)
		}
	} else {
		return fmt.Errorf("存储引擎服务未注册")
	}

	return nil
}

func main() {
	fmt.Println("WFS文件名批量更名工具")
	fmt.Println("========================")

	// 解析命令行参数
	if len(os.Args) > 1 {
		config.DataDir = os.Args[1]
	}

	fmt.Printf("数据目录: %s\n", config.DataDir)
	fmt.Printf("并发线程数: %d\n", config.WorkerCount)
	fmt.Printf("批处理大小: %d\n", config.BatchSize)
	fmt.Println()

	// 初始化系统
	if err := initSystem(); err != nil {
		fmt.Printf("初始化失败: %v\n", err)
		os.Exit(1)
	}

	// 开始处理
	if err := processAllFiles(); err != nil {
		fmt.Printf("处理失败: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("程序执行完成！")
}
